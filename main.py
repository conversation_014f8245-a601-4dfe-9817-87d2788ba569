from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any

from models import (
    RunResponse, VectorizeResponse, SearchResponse, SearchResult,
    MetadataSearchResponse, CollectionStatsResponse, DocumentResponse
)
from services.processing_service import process_request
from services.vector_service import VectorService
from utils.logging_config import setup_logging

# Configure logging
logger = setup_logging()

app = FastAPI(title="Docs + URLs → Azure OpenAI (BeautifulSoup) with LangChain Vector Storage", version="1.0.0")

# Add CORS middleware to allow all origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Initialize vector service
vector_service = VectorService()

@app.post("/run", response_model=RunResponse)
async def run(
    prompt: str = Form(..., description="Your instructions for OpenAI."),
    urls: Optional[List[str]] = Form(None, description="Repeat 'urls' field for multiple URLs."),
    model: str = Form("gpt-4.1", description="Azure OpenAI deployment name."),
    files: Optional[List[UploadFile]] = File(None, description="One or more documents."),
):
    """
    Accepts:
      - multipart/form-data
      - fields:
          prompt: str (required)
          urls: List[str] (optional; can repeat `urls` field)
          model: str (optional)
      - files: List[UploadFile] (optional)
    """
    urls = urls or []
    files = files or []

    # Process the request using the service layer
    output_text, tokens_used, sources = await process_request(
        prompt=prompt,
        urls=urls,
        model=model,
        files=files
    )

    return JSONResponse(
        RunResponse(
            model=model,
            tokens_used=tokens_used,
            output=output_text,
            sources=sources,
        ).model_dump()
    )

@app.post("/vectorize", response_model=VectorizeResponse)
async def vectorize_file(
    file: UploadFile = File(..., description="File to vectorize and store"),
    metadata: Optional[str] = Form(None, description="Additional metadata as JSON string")
):
    """
    Upload a file, extract text, create embeddings using OpenAI, and store in ChromaDB
    """
    try:
        # Parse metadata if provided
        additional_metadata = {}
        if metadata:
            import json
            try:
                additional_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid JSON metadata")
        
        # Vectorize and store the file
        result = await vector_service.vectorize_and_store_file(file, additional_metadata)
        
        return JSONResponse(VectorizeResponse(**result).model_dump())
        
    except Exception as e:
        logger.error(f"Error in vectorize endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Vectorization failed: {str(e)}")

@app.post("/search", response_model=SearchResponse)
async def search_documents(
    query: str = Form(..., description="Search query"),
    n_results: int = Form(5, description="Number of results to return (max 20)"),
    filter_metadata: Optional[str] = Form(None, description="Metadata filter as JSON string")
):
    """
    Search for similar documents using semantic similarity
    """
    try:
        # Limit results to prevent abuse
        n_results = min(max(1, n_results), 20)
        
        # Parse metadata filter if provided
        metadata_filter = None
        if filter_metadata:
            import json
            try:
                metadata_filter = json.loads(filter_metadata)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid JSON metadata filter")
        
        # Search for similar documents
        results = vector_service.search_similar(query, n_results, metadata_filter)
        
        return JSONResponse(SearchResponse(
            query=query,
            results=results,
            total_results=len(results)
        ).model_dump())
        
    except Exception as e:
        logger.error(f"Error in search endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.post("/search-by-metadata", response_model=MetadataSearchResponse)
async def search_by_metadata(
    metadata_filter: str = Form(..., description="Metadata filter as JSON string"),
    n_results: int = Form(10, description="Number of results to return (max 100)")
):
    """
    Search documents by metadata filters
    """
    try:
        # Limit results to prevent abuse
        n_results = min(max(1, n_results), 100)
        
        # Parse metadata filter
        import json
        try:
            filter_dict = json.loads(metadata_filter)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON metadata filter")
        
        # Search by metadata
        results = vector_service.search_by_metadata(filter_dict, n_results)
        
        return JSONResponse(MetadataSearchResponse(
            filter=filter_dict,
            results=results,
            total_results=len(results)
        ).model_dump())
        
    except Exception as e:
        logger.error(f"Error in metadata search endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Metadata search failed: {str(e)}")

@app.get("/document/{document_id}", response_model=DocumentResponse)
async def get_document(document_id: str):
    """
    Retrieve a specific document by its ID
    """
    try:
        document = vector_service.get_document_by_id(document_id)
        
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return JSONResponse(DocumentResponse(**document).model_dump())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving document {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve document: {str(e)}")

@app.delete("/documents")
async def delete_documents(
    metadata_filter: str = Query(..., description="Metadata filter as JSON string")
):
    """
    Delete documents matching metadata filter
    """
    try:
        # Parse metadata filter
        import json
        try:
            filter_dict = json.loads(metadata_filter)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON metadata filter")
        
        # Delete documents
        deleted_count = vector_service.delete_documents(filter_dict)
        
        return {"message": f"Deleted {deleted_count} documents", "deleted_count": deleted_count}
        
    except Exception as e:
        logger.error(f"Error deleting documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete documents: {str(e)}")

@app.post("/clear-collection")
async def clear_collection():
    """
    Clear all documents from the collection (use with caution!)
    """
    try:
        # Get initial count for user feedback
        initial_count = vector_service.vectorstore._collection.count()
        
        if initial_count == 0:
            return {"message": "Collection was already empty", "documents_cleared": 0}
        
        # Clear the collection
        success = vector_service.clear_collection()
        
        if success:
            # Verify the collection was cleared
            is_empty = vector_service.verify_collection_empty()
            
            if is_empty:
                return {
                    "message": "Collection cleared successfully", 
                    "documents_cleared": initial_count,
                    "verification": "Collection confirmed empty"
                }
            else:
                return {
                    "message": "Collection may not have been fully cleared", 
                    "documents_cleared": initial_count,
                    "verification": "Collection not confirmed empty - check logs",
                    "warning": "Some documents may still exist"
                }
        else:
            raise HTTPException(status_code=500, detail="Failed to clear collection")
            
    except Exception as e:
        logger.error(f"Error clearing collection: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear collection: {str(e)}")

@app.get("/vector-stats", response_model=CollectionStatsResponse)
async def get_vector_stats():
    """
    Get statistics about the document collection
    """
    try:
        stats = vector_service.get_collection_stats()
        return JSONResponse(CollectionStatsResponse(**stats).model_dump())
        
    except Exception as e:
        logger.error(f"Error in vector-stats endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")

@app.get("/vector-stats-detailed")
async def get_detailed_vector_stats():
    """
    Get detailed statistics about the document collection
    This endpoint provides more information but may take longer
    """
    try:
        # Get basic stats first
        basic_stats = vector_service.get_collection_stats()
        
        # Try to get some sample documents for additional info
        try:
            sample_docs = vector_service.search_similar("", n_results=10)
            file_types = {}
            total_text_length = 0
            
            for doc in sample_docs:
                metadata = doc.get("metadata", {})
                file_type = metadata.get("file_type", "unknown")
                file_types[file_type] = file_types.get(file_type, 0) + 1
                total_text_length += metadata.get("text_length", 0)
            
            detailed_stats = {
                **basic_stats,
                "sample_analyzed": len(sample_docs),
                "file_types_found": file_types,
                "average_text_length": total_text_length // len(sample_docs) if sample_docs else 0,
                "detailed_analysis": "Based on sample of documents"
            }
            
            return JSONResponse(detailed_stats)
            
        except Exception as e:
            logger.warning(f"Could not get detailed stats: {e}")
            # Return basic stats if detailed analysis fails
            return JSONResponse(basic_stats)
        
    except Exception as e:
        logger.error(f"Error in detailed vector-stats endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get detailed stats: {str(e)}")

@app.get("/")
def health():
    return {"ok": True, "service": "Docs + URLs → Azure OpenAI (BeautifulSoup) with LangChain Vector Storage"}

# ---------- local dev ----------
# Run with: uvicorn main:app --reload
