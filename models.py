from pydantic import BaseModel
from typing import List, Optional, Dict, Any

class RunResponse(BaseModel):
    model: str
    tokens_used: Optional[int] = None
    output: str
    sources: List[str]

class VectorizeResponse(BaseModel):
    document_id: str
    filename: str
    text_length: int
    chunks_created: int
    status: str

class SearchResponse(BaseModel):
    query: str
    results: List[Dict[str, Any]]
    total_results: int

class SearchResult(BaseModel):
    document: str
    metadata: Dict[str, Any]
    similarity_score: float
    distance: float

class MetadataSearchResponse(BaseModel):
    filter: Dict[str, Any]
    results: List[Dict[str, Any]]
    total_results: int

class CollectionStatsResponse(BaseModel):
    total_documents: int
    unique_files: str  # Changed to string to handle the N/A message
    total_chunks: int
    collection_name: str
    persist_directory: str
    embedding_model: str
    note: Optional[str] = None

class DocumentResponse(BaseModel):
    document: str
    metadata: Dict[str, Any]
