import logging
from typing import List, <PERSON><PERSON>
from openai import AzureOpenAI
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception

from config import AZURE_OPENAI_API_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION

logger = logging.getLogger(__name__)

# Azure OpenAI client
oai = AzureOpenAI(
    api_key=AZURE_OPENAI_API_KEY,
    azure_endpoint=AZURE_OPENAI_ENDPOINT,
    api_version=AZURE_OPENAI_API_VERSION
)

def build_user_prompt(instructions: str,
                      file_blobs: List[Tuple[str, str]],
                      url_blobs: List[Tuple[str, str]],
                      max_chars_per_source: int = 60_000) -> str:
    """
    Compose a single prompt string for OpenAI.
    Truncates very large sources to keep request size reasonable.
    """
    parts = [instructions.strip()]
    parts.append("\n\n### Sources\n")
    if file_blobs:
        parts.append("#### Uploaded Files\n")
        for label, text in file_blobs:
            t = (text or "")[:max_chars_per_source]
            parts.append(f"\n---\n# {label}\n{t}")
    if url_blobs:
        parts.append("\n\n#### Web URLs\n")
        for label, text in url_blobs:
            t = (text or "")[:max_chars_per_source]
            parts.append(f"\n---\n# {label}\n{t}")
    return "\n".join(parts).strip()

async def call_openai(prompt: str, model: str) -> tuple[str, int]:
    """
    Call Azure OpenAI Chat Completions API
    Returns (output_text, tokens_used)
    """
    try:
        resp = oai.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
        )
    except Exception as e:
        raise HTTPException(status_code=502, detail=f"OpenAI API error: {e}")

    # Extract the final text
    output_text = ""
    if resp.choices and len(resp.choices) > 0:
        output_text = resp.choices[0].message.content or ""

    # Token usage
    tokens_used = None
    if resp.usage:
        tokens_used = resp.usage.total_tokens

    return output_text, tokens_used
