# FastAPI NoteLM

A FastAPI application that processes documents and URLs using Azure OpenAI and BeautifulSoup for web scraping.

## Environment Setup

Create a `.env` file in the root directory with the following variables:

```bash
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_actual_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
```

## Running with Docker

### Option 1: Docker Compose (Recommended)

```bash
# Build and run with docker-compose
docker-compose up --build

# Run in background
docker-compose up -d --build
```

### Option 2: Docker Commands

```bash
# Build the image
docker build -t fastapi-notelm .

# Run with environment file
docker run -p 8000:8000 --env-file .env fastapi-notelm

# Or run with individual environment variables
docker run -p 8000:8000 \
  -e AZURE_OPENAI_API_KEY=your_key \
  -e AZURE_OPENAI_ENDPOINT=your_endpoint \
  -e AZURE_OPENAI_API_VERSION=2024-02-15-preview \
  fastapi-notelm
```

## Running Locally

```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
uvicorn main:app --reload
```

## API Endpoints

- `GET /` - Health check
- `POST /run` - Process documents and URLs with OpenAI

## Features

- Document processing (PDF, DOCX, TXT, MD, CSV, JSON, YAML)
- URL scraping with BeautifulSoup
- Azure OpenAI integration for AI-powered analysis
- CORS enabled for web applications
- Docker support for easy deployment
