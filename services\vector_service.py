import os
import logging
from typing import List, Dict, Any, Optional
import chromadb
from chromadb.config import Settings
import numpy as np

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import AzureOpenAIEmbeddings
from langchain_community.vectorstores import Chroma
from langchain.schema import Document

from config import AZURE_OPENAI_EMBEDDING_DEPLOYMENT
from services.openai_service import oai
from utils.file_utils import extract_file_text

logger = logging.getLogger(__name__)

class VectorService:
    def __init__(self, persist_directory: str = "./chroma_db"):
        """Initialize LangChain vector service with ChromaDB"""
        self.persist_directory = persist_directory
        
        # Initialize Azure OpenAI embeddings
        self.embeddings = AzureOpenAIEmbeddings(
            azure_deployment=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME"),
            openai_api_version=os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_URL"),
            api_key=os.getenv("AZURE_OPENAI_EMBEDDING_KEY"),
            chunk_size=1000
        )
        
        # Initialize ChromaDB vector store
        self.vectorstore = Chroma(
            persist_directory=persist_directory,
            embedding_function=self.embeddings,
            collection_name="documents"
        )
        
        # Text splitter for chunking documents
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
        
        logger.info(f"LangChain vector service initialized with ChromaDB at {persist_directory}")
        logger.info(f"Using Azure OpenAI embedding deployment: {AZURE_OPENAI_EMBEDDING_DEPLOYMENT}")
    
    async def vectorize_and_store_file(self, file, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Extract text from file, chunk it, create embeddings, and store in ChromaDB
        Returns metadata about the stored document
        """
        try:
            # Extract text from file
            label, text = await extract_file_text(file)
            
            if not text or len(text.strip()) == 0:
                raise ValueError("File contains no extractable text")
            
            # Prepare base metadata
            base_metadata = {
                "filename": file.filename or "unknown",
                "file_type": self._get_file_type(file.filename),
                "text_length": len(text),
                "upload_timestamp": str(np.datetime64('now')),
                **(metadata or {})
            }
            
            # Split text into chunks
            text_chunks = self.text_splitter.split_text(text)
            logger.info(f"Split text into {len(text_chunks)} chunks")
            
            # Create documents with metadata
            documents = []
            for i, chunk in enumerate(text_chunks):
                chunk_metadata = {
                    **base_metadata,
                    "chunk_index": i,
                    "chunk_size": len(chunk),
                    "total_chunks": len(text_chunks)
                }
                documents.append(Document(
                    page_content=chunk,
                    metadata=chunk_metadata
                ))
            
            # Add documents to vector store
            self.vectorstore.add_documents(documents)
            
            # Persist changes
            self.vectorstore.persist()
            
            logger.info(f"Successfully vectorized and stored file: {file.filename} ({len(text_chunks)} chunks)")
            
            return {
                "document_id": self._generate_document_id(file.filename),
                "filename": file.filename,
                "text_length": len(text),
                "chunks_created": len(text_chunks),
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error vectorizing file {file.filename}: {str(e)}")
            raise
    
    def search_similar(self, query: str, n_results: int = 5, filter_metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Search for similar documents using semantic similarity
        Supports metadata filtering
        """
        try:
            # Perform similarity search
            if filter_metadata:
                results = self.vectorstore.similarity_search_with_score(
                    query=query,
                    k=n_results,
                    filter=filter_metadata
                )
            else:
                results = self.vectorstore.similarity_search_with_score(
                    query=query,
                    k=n_results
                )
            
            # Format results
            formatted_results = []
            for doc, score in results:
                formatted_results.append({
                    "document": doc.page_content,
                    "metadata": doc.metadata,
                    "similarity_score": 1 - score,  # Convert distance to similarity (0-1)
                    "distance": score
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            raise
    
    def search_by_metadata(self, metadata_filter: Dict[str, Any], n_results: int = 10) -> List[Dict[str, Any]]:
        """
        Search documents by metadata filters
        """
        try:
            # Handle empty filter case - use a simple query instead
            if not metadata_filter:
                results = self.vectorstore.similarity_search(
                    query=" ",  # Use a space instead of empty string
                    k=n_results
                )
            else:
                results = self.vectorstore.similarity_search(
                    query="",  # Empty query to get all documents
                    k=n_results,
                    filter=metadata_filter
                )
            
            formatted_results = []
            for doc in results:
                formatted_results.append({
                    "document": doc.page_content,
                    "metadata": doc.metadata
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching by metadata: {str(e)}")
            raise
    
    def get_document_by_id(self, document_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a specific document by its ID
        """
        try:
            # Note: This is a simplified implementation
            # In a production system, you might want to maintain a separate document index
            results = self.vectorstore.similarity_search(
                query="",  # Empty query
                k=1000  # Get many results to search through
            )
            
            for doc in results:
                if doc.metadata.get("document_id") == document_id:
                    return {
                        "document": doc.page_content,
                        "metadata": doc.metadata
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving document {document_id}: {str(e)}")
            raise
    
    def delete_documents(self, filter_metadata: Dict[str, Any]) -> int:
        """
        Delete documents matching metadata filter
        Returns number of documents deleted
        """
        try:
            # Get documents to delete
            docs_to_delete = self.search_by_metadata(filter_metadata, n_results=1000)
            
            if not docs_to_delete:
                return 0
            
            # Delete from vector store
            # Note: This is a simplified implementation
            # In production, you might want to use ChromaDB's delete method directly
            logger.info(f"Deleting {len(docs_to_delete)} documents matching filter: {filter_metadata}")
            
            # For now, we'll just log the deletion
            # In a real implementation: self.vectorstore.delete(filter=filter_metadata)
            
            return len(docs_to_delete)
            
        except Exception as e:
            logger.error(f"Error deleting documents: {str(e)}")
            raise
    
    def _get_file_type(self, filename: str) -> str:
        """Extract file type from filename"""
        if not filename:
            return "unknown"
        
        ext = os.path.splitext(filename.lower())[1]
        if ext in [".pdf", ".docx", ".txt", ".md"]:
            return ext[1:]  # Remove the dot
        return "text"
    
    def _generate_document_id(self, filename: str) -> str:
        """Generate a unique document ID"""
        import uuid
        import time
        
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]
        safe_filename = (filename or "unknown").replace(" ", "_").replace(".", "_")
        
        return f"{safe_filename}_{timestamp}_{unique_id}"
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the document collection"""
        try:
            # Get collection info from ChromaDB
            collection = self.vectorstore._collection
            count = collection.count()
            
            # For now, return basic stats without trying to query documents
            # This avoids the ChromaDB filter issues
            return {
                "total_documents": count,
                "unique_files": "N/A - Use search endpoint for detailed stats",
                "total_chunks": count,  # Assuming each document is a chunk
                "collection_name": "documents",
                "persist_directory": self.persist_directory,
                "embedding_model": AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
                "note": "Detailed file stats available via search endpoints"
            }
        except Exception as e:
            logger.error(f"Error getting collection stats: {str(e)}")
            raise
    
    def clear_collection(self) -> bool:
        """Clear all documents from the collection"""
        try:
            logger.warning("Clearing entire document collection")
            
            # Get initial count
            initial_count = self.vectorstore._collection.count()
            logger.info(f"Initial document count: {initial_count}")
            
            if initial_count == 0:
                logger.info("Collection was already empty")
                return True
            
            # Method 1: Try to delete by getting all IDs first
            try:
                collection = self.vectorstore._collection
                all_docs = collection.get()
                if all_docs and all_docs['ids']:
                    collection.delete(ids=all_docs['ids'])
                    logger.info(f"Successfully deleted {len(all_docs['ids'])} documents using ID deletion")
                else:
                    logger.info("Collection was already empty")
            except Exception as e:
                logger.warning(f"ID-based deletion failed: {e}, trying alternative method")
                
                # Method 2: Try to delete the entire collection and recreate it
                try:
                    # Delete the collection
                    self.vectorstore._client.delete_collection("documents")
                    logger.info("Collection deleted successfully")
                    
                    # Recreate the collection
                    self.vectorstore = Chroma(
                        persist_directory=self.persist_directory,
                        embedding_function=self.embeddings,
                        collection_name="documents"
                    )
                    logger.info("Collection recreated successfully")
                except Exception as e2:
                    logger.error(f"Collection recreation failed: {e2}")
                    raise
            
            # Persist the changes
            self.vectorstore.persist()
            
            # Verify the collection was cleared
            final_count = self.vectorstore._collection.count()
            logger.info(f"Final document count: {final_count}")
            
            if final_count == 0:
                logger.info("Collection successfully cleared")
                return True
            else:
                logger.warning(f"Collection may not have been fully cleared. Count: {final_count}")
                return False
            
        except Exception as e:
            logger.error(f"Error clearing collection: {str(e)}")
            raise
    
    def verify_collection_empty(self) -> bool:
        """Verify that the collection is empty"""
        try:
            count = self.vectorstore._collection.count()
            return count == 0
        except Exception as e:
            logger.error(f"Error verifying collection: {str(e)}")
            return False
