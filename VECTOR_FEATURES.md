# LangChain Vector Storage Features

This application now includes advanced vector storage capabilities using **<PERSON><PERSON><PERSON><PERSON>** with OpenAI embeddings and ChromaDB for semantic search and document retrieval.

## 🚀 Enhanced Endpoints

### 1. Vectorize File (`POST /vectorize`)
Upload a file to extract text, chunk it intelligently, create embeddings using OpenAI, and store in ChromaDB.

**Request:**
- `file`: File to upload (supports PDF, DOCX, TXT, MD, etc.)
- `metadata` (optional): JSON string with additional metadata

**Response:**
```json
{
  "document_id": "filename_timestamp_uniqueid",
  "filename": "document.pdf",
  "text_length": 1500,
  "chunks_created": 3,
  "status": "success"
}
```

**Example:**
```bash
curl -X POST "http://localhost:8000/vectorize" \
  -F "file=@document.pdf" \
  -F "metadata={\"category\": \"research\", \"author\": \"<PERSON>\"}"
```

### 2. Semantic Search (`POST /search`)
Search for semantically similar documents using natural language queries with optional metadata filtering.

**Request:**
- `query`: Search query text
- `n_results`: Number of results to return (1-20, default: 5)
- `filter_metadata` (optional): JSON string for metadata filtering

**Response:**
```json
{
  "query": "machine learning algorithms",
  "results": [
    {
      "document": "Document text content...",
      "metadata": {
        "filename": "ml_paper.pdf",
        "file_type": "pdf",
        "chunk_index": 0,
        "chunk_size": 1000
      },
      "similarity_score": 0.877,
      "distance": 0.123
    }
  ],
  "total_results": 1
}
```

**Example with metadata filter:**
```bash
curl -X POST "http://localhost:8000/search" \
  -F "query=machine learning algorithms" \
  -F "n_results=3" \
  -F "filter_metadata={\"file_type\": \"pdf\"}"
```

### 3. Metadata Search (`POST /search-by-metadata`)
Search documents by metadata filters without semantic similarity.

**Request:**
- `metadata_filter`: JSON string with metadata criteria
- `n_results`: Number of results to return (1-100, default: 10)

**Example:**
```bash
curl -X POST "http://localhost:8000/search-by-metadata" \
  -F "metadata_filter={\"file_type\": \"pdf\", \"category\": \"research\"}" \
  -F "n_results=20"
```

### 4. Document Retrieval (`GET /document/{document_id}`)
Retrieve a specific document by its ID.

**Response:**
```json
{
  "document": "Document content...",
  "metadata": {
    "filename": "document.pdf",
    "file_type": "pdf",
    "chunk_index": 0,
    "chunk_size": 1000
  }
}
```

### 5. Document Management (`DELETE /documents`)
Delete documents matching metadata filters.

**Request:**
- `metadata_filter`: Query parameter with JSON metadata filter

**Example:**
```bash
curl -X DELETE "http://localhost:8000/documents?metadata_filter={\"filename\": \"old_document.pdf\"}"
```

### 6. Collection Management (`POST /clear-collection`)
Clear all documents from the collection (use with caution!).

### 7. Collection Statistics (`GET /vector-stats`)
Get comprehensive statistics about the document collection.

**Response:**
```json
{
  "total_documents": 45,
  "unique_files": 15,
  "total_chunks": 45,
  "collection_name": "documents",
  "persist_directory": "./chroma_db",
  "embedding_model": "text-embedding-ada-002"
}
```

## 🔧 Setup Requirements

1. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Environment Variables:**
   Ensure your `.env` file contains:
   ```
   AZURE_OPENAI_API_KEY=your_key_here
   AZURE_OPENAI_ENDPOINT=your_endpoint_here
   AZURE_OPENAI_API_VERSION=2024-02-15-preview
   ```

3. **ChromaDB Storage:**
   - Documents are automatically stored in `./chroma_db/`
   - The directory is created automatically on first use
   - Data persists between application restarts

## 📁 Supported File Types

- **Text Files:** `.txt`, `.md`, `.csv`, `.json`, `.yaml`, `.yml`, `.log`
- **Documents:** `.pdf` (requires `pypdf`), `.docx` (requires `python-docx`)
- **Web Content:** URLs (scraped and vectorized)

## 🧠 How LangChain Enhances the Solution

### **Intelligent Text Chunking:**
- **RecursiveCharacterTextSplitter** with optimal chunk sizes (1000 chars)
- **Overlap handling** (200 chars) for context preservation
- **Smart separators** (paragraphs, sentences, words)

### **Advanced Vector Operations:**
- **LangChain OpenAI embeddings** with proper Azure integration
- **ChromaDB integration** through LangChain's vector store abstraction
- **Metadata filtering** for precise document retrieval
- **Similarity scoring** with distance-to-similarity conversion

### **Document Management:**
- **Chunk-level metadata** for granular organization
- **File-level aggregation** for comprehensive statistics
- **Flexible search** by content, metadata, or both

## 🚀 Performance Features

- **Embedding Model:** OpenAI's `text-embedding-ada-002` (1536 dimensions)
- **Search Algorithm:** HNSW with cosine similarity
- **Storage:** Persistent ChromaDB with automatic indexing
- **Chunking:** Optimal text splitting for better search relevance
- **Scalability:** Supports thousands of documents efficiently

## 💡 Advanced Use Cases

### **Metadata Organization:**
```json
{
  "category": "research",
  "author": "John Doe",
  "department": "AI Lab",
  "project": "Machine Learning",
  "date_created": "2024-01-15"
}
```

### **Filtered Searches:**
- Find all PDF research papers by a specific author
- Search within specific categories or date ranges
- Combine semantic search with metadata constraints

### **Document Lifecycle:**
- Upload and vectorize documents
- Search and retrieve relevant content
- Update metadata as documents evolve
- Archive or delete outdated documents

## 🔒 Security & Best Practices

- **Files processed in memory** - no permanent file storage
- **Text content and embeddings only** - no raw file retention
- **Metadata validation** - JSON parsing with error handling
- **Result limiting** - prevents abuse and ensures performance
- **Comprehensive logging** - audit trail for all operations

## 🧪 Testing

Test the enhanced functionality:
```bash
# Start the application
uvicorn main:app --reload

# Test vectorization
curl -X POST "http://localhost:8000/vectorize" \
  -F "file=@your_document.pdf"

# Test semantic search
curl -X POST "http://localhost:8000/search" \
  -F "query=your search query"

# Test metadata search
curl -X POST "http://localhost:8000/search-by-metadata" \
  -F "metadata_filter={\"file_type\": \"pdf\"}"
```

## 🎯 Benefits of LangChain Integration

1. **Production Ready:** Industry-standard framework for LLM applications
2. **Better Chunking:** Intelligent text splitting for optimal search
3. **Enhanced Metadata:** Rich document organization and filtering
4. **Scalable Architecture:** Built for enterprise document management
5. **Future Extensibility:** Easy to add RAG, chains, and agents
