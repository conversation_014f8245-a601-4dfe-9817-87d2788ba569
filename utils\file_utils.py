import os
import tempfile
import shutil
from typing import <PERSON><PERSON>
from fastapi import UploadFile, HTTPException

from config import TEXT_EXTS

# Optional text extractors
try:
    from pypdf import PdfReader
    HAS_PDF = True
except Exception:
    HAS_PDF = False

try:
    import docx  # python-docx
    HAS_DOCX = True
except Exception:
    HAS_DOCX = False

def _ext(path: str) -> str:
    """Get file extension from path"""
    return os.path.splitext((path or "").lower())[1]

async def read_text_file(file: UploadFile) -> str:
    """Read text file with best-effort encoding detection"""
    raw = await file.read()
    # best-effort decode
    for enc in ("utf-8", "utf-16", "latin-1"):
        try:
            return raw.decode(enc)
        except Exception:
            continue
    return raw.decode("utf-8", errors="ignore")

async def read_pdf(file: UploadFile) -> str:
    """Read PDF file using pypdf"""
    if not HAS_PDF:
        raise HTTPException(
            status_code=400,
            detail="PDF support requires `pypdf`. Install with: pip install pypdf"
        )
    # pypdf expects a file-like object; UploadFile.file is already one
    try:
        reader = PdfReader(file.file)
        pages = [p.extract_text() or "" for p in reader.pages]
        return "\n".join(pages).strip()
    finally:
        await file.seek(0)

async def read_docx(file: UploadFile) -> str:
    """Read DOCX file using python-docx"""
    if not HAS_DOCX:
        raise HTTPException(
            status_code=400,
            detail="DOCX support requires `python-docx`. Install with: pip install python-docx"
        )
    # Need to save to temp for python-docx
    suffix = ".docx"
    with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
        await file.seek(0)
        shutil.copyfileobj(file.file, tmp)
        tmp_path = tmp.name
    try:
        d = docx.Document(tmp_path)
        return "\n".join(p.text for p in d.paragraphs)
    finally:
        try:
            os.remove(tmp_path)
        except Exception:
            pass

async def extract_file_text(file: UploadFile) -> Tuple[str, str]:
    """
    Extract text from uploaded file.
    Returns (label, text) where label is a human-friendly descriptor for later attribution
    """
    name = file.filename or "uploaded"
    ext = _ext(name)
    try:
        if ext in TEXT_EXTS:
            text = await read_text_file(file)
        elif ext == ".pdf":
            text = await read_pdf(file)
        elif ext == ".docx":
            text = await read_docx(file)
        else:
            # fallback: try as text
            text = await read_text_file(file)
        return (f"FILE: {name}", text.strip())
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to read {name}: {e}")
