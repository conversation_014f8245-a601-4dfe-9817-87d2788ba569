import logging
from typing import List, <PERSON><PERSON>
from fastapi import UploadFile

from utils.file_utils import extract_file_text
from utils.web_utils import scrape_urls
from services.openai_service import build_user_prompt, call_openai

logger = logging.getLogger(__name__)

async def process_request(
    prompt: str,
    urls: List[str],
    model: str,
    files: List[UploadFile]
) -> tuple[str, int, List[str]]:
    """
    Main processing function that handles files, URLs, and OpenAI API call
    Returns (output_text, tokens_used, sources)
    """
    # 1) Read files
    file_blobs: List[Tuple[str, str]] = []
    for f in files:
        if not f:
            continue
        file_blobs.append(await extract_file_text(f))

    # 2) Scrape URLs
    url_blobs: List[Tuple[str, str]] = []
    if urls:
        url_blobs = await scrape_urls(urls)

    if not (file_blobs or url_blobs):
        # You can still run with just instructions, but warn if no sources
        pass

    # 3) Build combined user prompt
    combined = build_user_prompt(prompt, file_blobs, url_blobs)

    # Log the final prompt being sent to OpenAI
    logger.info("=" * 80)
    logger.info("SENDING PROMPT TO OPENAI:")
    logger.info("=" * 80)
    logger.info(f"Model: {model}")
    logger.info(f"Prompt length: {len(combined)} characters")
    logger.info("Prompt content:")
    logger.info("-" * 40)
    logger.info(combined)
    logger.info("-" * 40)

    # 4) Call OpenAI Chat Completions API
    output_text, tokens_used = await call_openai(combined, model)

    # Log the response from OpenAI
    logger.info("=" * 80)
    logger.info("RECEIVED RESPONSE FROM OPENAI:")
    logger.info("=" * 80)
    logger.info(f"Tokens used: {tokens_used}")
    logger.info(f"Response length: {len(output_text)} characters")
    logger.info("Response content:")
    logger.info("-" * 40)
    logger.info(output_text)
    logger.info("-" * 40)

    # Build source list for transparency
    sources = [lbl for (lbl, _) in file_blobs] + [lbl for (lbl, _) in url_blobs]

    return output_text, tokens_used, sources
