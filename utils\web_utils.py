import asyncio
import requests
from typing import List, <PERSON><PERSON>
from bs4 import BeautifulSoup
import html2text

# HTML to Markdown converter
html_converter = html2text.HTML2Text()
html_converter.ignore_links = False
html_converter.ignore_images = False
html_converter.body_width = 0  # No line wrapping

async def scrape_urls(urls: List[str]) -> List[Tuple[str, str]]:
    """
    BeautifulSoup scrape for each URL. Returns list of (label, text)
    """
    results: List[Tuple[str, str]] = []

    async def _one(url: str):
        try:
            # Send HTTP request with headers to avoid being blocked
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # Parse HTML with BeautifulSoup
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()
            
            # Convert HTML to markdown
            html_content = str(soup)
            markdown_content = html_converter.handle(html_content)
            
            # Clean up the markdown content
            cleaned_content = markdown_content.strip()
            
            return (f"URL: {url}", cleaned_content)
        except Exception as e:
            return (f"URL: {url}", f"[Error scraping: {e}]")

    tasks = [_one(u) for u in urls]
    for r in await asyncio.gather(*tasks):
        results.append(r)
    return results
